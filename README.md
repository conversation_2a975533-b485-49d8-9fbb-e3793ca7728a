# XLY Design University Website

A modern, responsive university website built with React, TypeScript, and Tailwind CSS. This project showcases a comprehensive university landing page with multiple sections and pages, featuring a sophisticated design system based on Klein Blue (#002fa7) as the primary accent color.

## 🚀 Features

- **Modern Design**: Clean, professional design with Klein Blue theme
- **Responsive Layout**: Fully responsive design that works on all devices
- **Interactive Components**: Smooth animations and transitions using Framer Motion
- **Multi-page Navigation**: Complete routing system with React Router
- **Comprehensive UI**: Built with shadcn/ui components for consistency
- **Accessibility**: WCAG compliant with proper semantic HTML
- **Performance Optimized**: Fast loading with Vite build system

## 📋 Pages & Sections

### Landing Page
- Hero section with carousel
- Key metrics with animated counters
- Academic and admissions overview
- News and events tabs
- Research highlights
- Campus life preview
- Footer with newsletter signup

### Additional Pages
- **About**: University mission, vision, history, and leadership
- **Academics**: Schools, programs, and international opportunities
- **Research**: Research areas, centers, and achievements
- **Campus Life**: Housing, dining, sports, and student organizations
- **Admissions**: Application process, deadlines, and financial aid

## 🛠 Tech Stack

- **Frontend Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui (Radix UI primitives)
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **Routing**: React Router DOM
- **Carousel**: Embla Carousel

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd xly-design-university
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open in browser**
   Navigate to `http://localhost:5173`

## 🏗 Project Structure

```
src/
├── components/
│   └── ui/                 # shadcn/ui components
│       ├── button.tsx
│       ├── card.tsx
│       ├── dialog.tsx
│       └── ...
├── pages/                  # Page components
│   ├── LandingPage.tsx    # Main landing page
│   ├── AboutPage.tsx      # About university
│   ├── AcademicsPage.tsx  # Academic programs
│   ├── ResearchPage.tsx   # Research areas
│   ├── CampusLifePage.tsx # Campus life
│   └── AdmissionsPage.tsx # Admissions info
├── lib/
│   └── utils.ts           # Utility functions
├── App.tsx                # Main app component
├── main.tsx              # Entry point
└── index.css             # Global styles
```

## 🎨 Design System

### Colors
- **Primary**: Klein Blue (#002fa7)
- **Background**: White (#ffffff)
- **Text**: Gray scale (gray-900, gray-700, gray-600)
- **Borders**: Primary with opacity variations

### Typography
- **Headings**: Bold, Klein Blue for primary headings
- **Body**: Gray-700 for main content
- **Captions**: Gray-600 for secondary information

### Components
- **Cards**: Subtle borders with hover effects
- **Buttons**: Primary (Klein Blue), outline, and ghost variants
- **Navigation**: Sticky header with backdrop blur
- **Animations**: Smooth transitions and scroll-triggered animations

## 🔧 Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### Adding New Components

1. Create component in appropriate directory
2. Follow TypeScript conventions
3. Use Tailwind CSS for styling
4. Implement proper accessibility features

### Customizing Theme

The theme is configured in `tailwind.config.js`. The Klein Blue color system is defined in the color palette:

```javascript
'klein-blue': {
  50: '#f0f4ff',
  // ... other shades
  900: '#002fa7', // Main Klein Blue
}
```

## 📱 Responsive Design

The website is fully responsive with breakpoints:
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

## ♿ Accessibility

- Semantic HTML structure
- ARIA labels and roles
- Keyboard navigation support
- Screen reader compatibility
- Color contrast compliance

## 🚀 Deployment

### Build for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

### Deploy to Vercel

1. Connect your repository to Vercel
2. Set build command: `npm run build`
3. Set output directory: `dist`
4. Deploy

### Deploy to Netlify

1. Connect your repository to Netlify
2. Set build command: `npm run build`
3. Set publish directory: `dist`
4. Deploy

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- **shadcn/ui** for the excellent component library
- **Tailwind CSS** for the utility-first CSS framework
- **Framer Motion** for smooth animations
- **Tokyo University of Science** for design inspiration

## 📞 Support

For questions or support, please open an issue in the repository or contact the development team.

---

Built with ❤️ using modern web technologies
