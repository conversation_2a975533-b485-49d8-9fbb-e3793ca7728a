import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, FlaskConical, Lightbulb, Users, TrendingUp, ChevronRight } from 'lucide-react';
import { Link } from 'react-router-dom';

const ACCENT = "#002fa7";

export default function ResearchPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 h-16 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-9 w-9 rounded-xl grid place-items-center text-white" style={{ backgroundColor: ACCENT }}>U</div>
            <Link to="/" className="font-semibold tracking-wide">理工大学 · University of Science</Link>
          </div>
          <Link to="/">
            <Button variant="ghost" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              返回首页
            </Button>
          </Link>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-16 bg-gradient-to-b from-gray-50 to-white">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold" style={{ color: ACCENT }}>科学研究</h1>
            <p className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
              面向社会需求的科研，以重大科学问题与产业关键技术为牵引，建设开放的交叉研究平台
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <main className="py-16">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          {/* Research Areas */}
          <section className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-8" style={{ color: ACCENT }}>重点研究领域</h2>
            <div className="grid md:grid-cols-2 gap-8">
              {[
                {
                  title: "先进材料",
                  description: "从计算预测到实验验证的闭环研发",
                  details: "纳米材料、智能材料、生物材料、能源材料等前沿材料的设计、制备与应用研究",
                  projects: 25,
                  funding: "5000万",
                  publications: 150
                },
                {
                  title: "信息与智能",
                  description: "大模型、边缘智能与可信 AI",
                  details: "人工智能、机器学习、计算机视觉、自然语言处理、智能系统等技术研发",
                  projects: 30,
                  funding: "6000万",
                  publications: 200
                },
                {
                  title: "生命与健康",
                  description: "合成生物学与生物医工交叉",
                  details: "生物技术、基因工程、药物研发、医疗器械、精准医学等生命科学研究",
                  projects: 20,
                  funding: "4500万",
                  publications: 120
                },
                {
                  title: "能源与环境",
                  description: "零碳技术与循环经济",
                  details: "可再生能源、储能技术、环境治理、碳中和技术、可持续发展等研究",
                  projects: 18,
                  funding: "4000万",
                  publications: 100
                }
              ].map((area, index) => (
                <Card key={index} className="border hover:shadow-lg transition-shadow" style={{ borderColor: ACCENT + "20" }}>
                  <CardHeader>
                    <CardTitle style={{ color: ACCENT }}>{area.title}</CardTitle>
                    <CardDescription className="text-base">{area.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700 mb-4">{area.details}</p>
                    <div className="grid grid-cols-3 gap-4 mb-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold" style={{ color: ACCENT }}>{area.projects}</div>
                        <div className="text-xs text-gray-600">在研项目</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold" style={{ color: ACCENT }}>{area.funding}</div>
                        <div className="text-xs text-gray-600">研究经费</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold" style={{ color: ACCENT }}>{area.publications}</div>
                        <div className="text-xs text-gray-600">年发表论文</div>
                      </div>
                    </div>
                    <Button variant="outline" className="w-full" style={{ borderColor: ACCENT + "40" }}>
                      了解更多 <ChevronRight className="ml-2 h-4 w-4" />
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>

          {/* Research Centers */}
          <section className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-8" style={{ color: ACCENT }}>研究中心</h2>
            <div className="grid md:grid-cols-3 gap-6">
              {[
                "国家重点实验室",
                "工程技术研究中心",
                "国际联合实验室",
                "产学研合作基地",
                "创新创业孵化器",
                "技术转移中心"
              ].map((center, index) => (
                <Card key={index} className="border text-center p-6 hover:shadow-lg transition-shadow" style={{ borderColor: ACCENT + "20" }}>
                  <FlaskConical className="h-12 w-12 mx-auto mb-4" style={{ color: ACCENT }} />
                  <h3 className="font-semibold text-lg" style={{ color: ACCENT }}>{center}</h3>
                  <p className="text-gray-600 text-sm mt-2">专业研究平台，推动科技创新与成果转化</p>
                </Card>
              ))}
            </div>
          </section>

          {/* Research Achievements */}
          <section className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-8" style={{ color: ACCENT }}>研究成果</h2>
            <div className="grid lg:grid-cols-4 gap-6">
              {[
                { icon: <TrendingUp className="h-8 w-8" />, number: "500+", label: "年发表论文" },
                { icon: <Lightbulb className="h-8 w-8" />, number: "200+", label: "专利申请" },
                { icon: <Users className="h-8 w-8" />, number: "50+", label: "国际合作" },
                { icon: <FlaskConical className="h-8 w-8" />, number: "30+", label: "重大项目" },
              ].map((achievement, index) => (
                <Card key={index} className="text-center p-6 border" style={{ borderColor: ACCENT + "20" }}>
                  <div className="flex justify-center mb-4">
                    <div className="p-3 rounded-full" style={{ backgroundColor: ACCENT + "10", color: ACCENT }}>
                      {achievement.icon}
                    </div>
                  </div>
                  <div className="text-3xl font-bold mb-2" style={{ color: ACCENT }}>{achievement.number}</div>
                  <div className="text-gray-600">{achievement.label}</div>
                </Card>
              ))}
            </div>
          </section>

          {/* Featured Research */}
          <section>
            <h2 className="text-3xl font-bold text-center mb-8" style={{ color: ACCENT }}>重点项目</h2>
            <div className="space-y-6">
              {[
                {
                  title: "AI驱动的材料发现加速平台",
                  category: "先进材料",
                  description: "基于机器学习和高通量计算的新材料设计与发现平台，大幅缩短材料研发周期",
                  status: "进行中",
                  funding: "2000万",
                  duration: "2023-2026"
                },
                {
                  title: "量子信息处理芯片",
                  category: "信息技术",
                  description: "面向量子计算的专用芯片设计与制造技术，推动量子计算产业化应用",
                  status: "进行中",
                  funding: "1500万",
                  duration: "2024-2027"
                },
                {
                  title: "智能生物传感器系统",
                  category: "生命科学",
                  description: "基于纳米技术的高灵敏度生物传感器，用于疾病早期诊断和健康监测",
                  status: "进行中",
                  funding: "1200万",
                  duration: "2023-2025"
                }
              ].map((project, index) => (
                <Card key={index} className="border" style={{ borderColor: ACCENT + "20" }}>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="text-xl font-semibold" style={{ color: ACCENT }}>{project.title}</h3>
                        <Badge variant="secondary" className="mt-2">{project.category}</Badge>
                      </div>
                      <Badge variant="outline" style={{ borderColor: ACCENT + "40", color: ACCENT }}>
                        {project.status}
                      </Badge>
                    </div>
                    <p className="text-gray-700 mb-4">{project.description}</p>
                    <div className="flex gap-6 text-sm text-gray-600">
                      <span>经费：{project.funding}</span>
                      <span>周期：{project.duration}</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>
        </div>
      </main>
    </div>
  );
}
