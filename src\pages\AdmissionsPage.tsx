import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, GraduationCap, DollarSign, Calendar, FileText, CheckCircle } from 'lucide-react';
import { Link } from 'react-router-dom';

const ACCENT = "#002fa7";

export default function AdmissionsPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 h-16 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-9 w-9 rounded-xl grid place-items-center text-white" style={{ backgroundColor: ACCENT }}>U</div>
            <Link to="/" className="font-semibold tracking-wide">理工大学 · University of Science</Link>
          </div>
          <Link to="/">
            <Button variant="ghost" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              返回首页
            </Button>
          </Link>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-16 bg-gradient-to-b from-gray-50 to-white">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold" style={{ color: ACCENT }}>招生与资助</h1>
            <p className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
              面向本科与研究生的多元入学途径与奖助体系，为学术发展提供坚实支持
            </p>
            <div className="mt-8">
              <Button size="lg" style={{ backgroundColor: ACCENT }}>
                立即申请
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <main className="py-16">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          {/* Application Deadlines */}
          <section className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-8" style={{ color: ACCENT }}>重要时间节点</h2>
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-8">
              <div className="grid md:grid-cols-3 gap-6">
                <Card className="border" style={{ borderColor: ACCENT + "20" }}>
                  <CardHeader className="text-center">
                    <Calendar className="h-8 w-8 mx-auto mb-2" style={{ color: ACCENT }} />
                    <CardTitle style={{ color: ACCENT }}>早鸟批次</CardTitle>
                    <CardDescription>2025年10月1日截止</CardDescription>
                  </CardHeader>
                  <CardContent className="text-center">
                    <Badge variant="secondary" style={{ backgroundColor: ACCENT + "10", color: ACCENT }}>
                      正在开放
                    </Badge>
                    <p className="text-sm text-gray-600 mt-2">享受优先审核和奖学金评定</p>
                  </CardContent>
                </Card>

                <Card className="border" style={{ borderColor: ACCENT + "20" }}>
                  <CardHeader className="text-center">
                    <Calendar className="h-8 w-8 mx-auto mb-2" style={{ color: ACCENT }} />
                    <CardTitle style={{ color: ACCENT }}>常规批次</CardTitle>
                    <CardDescription>2026年1月15日截止</CardDescription>
                  </CardHeader>
                  <CardContent className="text-center">
                    <Badge variant="outline">即将开放</Badge>
                    <p className="text-sm text-gray-600 mt-2">标准申请流程和时间安排</p>
                  </CardContent>
                </Card>

                <Card className="border" style={{ borderColor: ACCENT + "20" }}>
                  <CardHeader className="text-center">
                    <Calendar className="h-8 w-8 mx-auto mb-2" style={{ color: ACCENT }} />
                    <CardTitle style={{ color: ACCENT }}>补录批次</CardTitle>
                    <CardDescription>2026年3月1日截止</CardDescription>
                  </CardHeader>
                  <CardContent className="text-center">
                    <Badge variant="outline">待开放</Badge>
                    <p className="text-sm text-gray-600 mt-2">针对剩余名额的补充录取</p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </section>

          {/* Admission Programs */}
          <section className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-8" style={{ color: ACCENT }}>招生项目</h2>
            <div className="grid lg:grid-cols-2 gap-8">
              <Card className="border" style={{ borderColor: ACCENT + "20" }}>
                <CardHeader>
                  <GraduationCap className="h-8 w-8 mb-2" style={{ color: ACCENT }} />
                  <CardTitle style={{ color: ACCENT }}>本科生招生</CardTitle>
                  <CardDescription>4年制学士学位项目，33个专业方向</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold mb-2">申请要求</h4>
                      <ul className="text-sm space-y-1">
                        <li>• 高中毕业证书或同等学历</li>
                        <li>• 高考成绩或国际标准化考试成绩</li>
                        <li>• 英语水平证明（TOEFL/IELTS）</li>
                        <li>• 推荐信2封</li>
                        <li>• 个人陈述</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">招生计划</h4>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>国内学生：3,000名</div>
                        <div>国际学生：500名</div>
                      </div>
                    </div>
                    <Button className="w-full" style={{ backgroundColor: ACCENT }}>
                      本科申请
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card className="border" style={{ borderColor: ACCENT + "20" }}>
                <CardHeader>
                  <GraduationCap className="h-8 w-8 mb-2" style={{ color: ACCENT }} />
                  <CardTitle style={{ color: ACCENT }}>研究生招生</CardTitle>
                  <CardDescription>硕士和博士学位项目，多个研究方向</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold mb-2">申请要求</h4>
                      <ul className="text-sm space-y-1">
                        <li>• 学士学位证书（硕士申请）</li>
                        <li>• 硕士学位证书（博士申请）</li>
                        <li>• 学术成绩单</li>
                        <li>• 研究计划书</li>
                        <li>• 推荐信3封</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">招生计划</h4>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>硕士生：1,200名</div>
                        <div>博士生：300名</div>
                      </div>
                    </div>
                    <Button className="w-full" style={{ backgroundColor: ACCENT }}>
                      研究生申请
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </section>

          {/* Financial Aid */}
          <section className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-8" style={{ color: ACCENT }}>奖助学金</h2>
            <div className="grid md:grid-cols-3 gap-6">
              {[
                {
                  title: "学业奖学金",
                  amount: "5,000-20,000元",
                  description: "基于学术成绩的奖励",
                  coverage: "学费减免",
                  recipients: "30%学生"
                },
                {
                  title: "科研奖学金",
                  amount: "10,000-50,000元",
                  description: "优秀科研成果奖励",
                  coverage: "研究经费",
                  recipients: "研究生"
                },
                {
                  title: "国际学生奖学金",
                  amount: "全额-部分学费",
                  description: "支持国际学生学习",
                  coverage: "学费+生活费",
                  recipients: "国际学生"
                }
              ].map((scholarship, index) => (
                <Card key={index} className="border" style={{ borderColor: ACCENT + "20" }}>
                  <CardHeader>
                    <DollarSign className="h-8 w-8 mb-2" style={{ color: ACCENT }} />
                    <CardTitle style={{ color: ACCENT }}>{scholarship.title}</CardTitle>
                    <CardDescription>{scholarship.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 text-sm">
                      <div><strong>金额：</strong>{scholarship.amount}</div>
                      <div><strong>覆盖：</strong>{scholarship.coverage}</div>
                      <div><strong>对象：</strong>{scholarship.recipients}</div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>

          {/* Application Process */}
          <section className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-8" style={{ color: ACCENT }}>申请流程</h2>
            <div className="max-w-4xl mx-auto">
              <div className="space-y-6">
                {[
                  { step: 1, title: "在线申请", description: "填写申请表格，上传必要文件", icon: <FileText className="h-6 w-6" /> },
                  { step: 2, title: "材料审核", description: "招生委员会审核申请材料", icon: <CheckCircle className="h-6 w-6" /> },
                  { step: 3, title: "面试评估", description: "部分专业需要面试或作品展示", icon: <GraduationCap className="h-6 w-6" /> },
                  { step: 4, title: "录取通知", description: "发放录取通知书和奖学金信息", icon: <CheckCircle className="h-6 w-6" /> },
                  { step: 5, title: "入学确认", description: "确认入学意向，缴纳学费", icon: <DollarSign className="h-6 w-6" /> },
                ].map((process, index) => (
                  <Card key={index} className="border" style={{ borderColor: ACCENT + "20" }}>
                    <CardContent className="p-6">
                      <div className="flex items-center gap-4">
                        <div className="flex-shrink-0">
                          <div className="w-12 h-12 rounded-full flex items-center justify-center text-white" style={{ backgroundColor: ACCENT }}>
                            {process.step}
                          </div>
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-lg" style={{ color: ACCENT }}>{process.title}</h3>
                          <p className="text-gray-600">{process.description}</p>
                        </div>
                        <div className="flex-shrink-0" style={{ color: ACCENT }}>
                          {process.icon}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </section>

          {/* Contact Information */}
          <section>
            <h2 className="text-3xl font-bold text-center mb-8" style={{ color: ACCENT }}>联系我们</h2>
            <Card className="border max-w-2xl mx-auto" style={{ borderColor: ACCENT + "20" }}>
              <CardContent className="p-8 text-center">
                <h3 className="text-xl font-semibold mb-4" style={{ color: ACCENT }}>招生办公室</h3>
                <div className="space-y-2 text-gray-700">
                  <p>电话：+86-010-1234-5678</p>
                  <p>邮箱：<EMAIL></p>
                  <p>地址：〇〇市〇〇区 学院路 1 号</p>
                  <p>办公时间：周一至周五 9:00-17:00</p>
                </div>
                <div className="mt-6 space-x-4">
                  <Button style={{ backgroundColor: ACCENT }}>在线咨询</Button>
                  <Button variant="outline" style={{ borderColor: ACCENT + "40" }}>预约参观</Button>
                </div>
              </CardContent>
            </Card>
          </section>
        </div>
      </main>
    </div>
  );
}
