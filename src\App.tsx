import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { Toaster } from '@/components/ui/toaster'
import LandingPage from '@/pages/LandingPage'
import AboutPage from '@/pages/AboutPage'
import AcademicsPage from '@/pages/AcademicsPage'
import ResearchPage from '@/pages/ResearchPage'
import CampusLifePage from '@/pages/CampusLifePage'
import AdmissionsPage from '@/pages/AdmissionsPage'

function App() {
  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path="/" element={<LandingPage />} />
          <Route path="/about" element={<AboutPage />} />
          <Route path="/academics" element={<AcademicsPage />} />
          <Route path="/research" element={<ResearchPage />} />
          <Route path="/campus-life" element={<CampusLifePage />} />
          <Route path="/admissions" element={<AdmissionsPage />} />
        </Routes>
        <Toaster />
      </div>
    </Router>
  )
}

export default App
