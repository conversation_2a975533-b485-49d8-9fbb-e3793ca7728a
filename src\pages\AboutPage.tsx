import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, Users, Building2, Globe, Award } from 'lucide-react';
import { Link } from 'react-router-dom';

const ACCENT = "#002fa7";

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 h-16 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-9 w-9 rounded-xl grid place-items-center text-white" style={{ backgroundColor: ACCENT }}>U</div>
            <Link to="/" className="font-semibold tracking-wide">理工大学 · University of Science</Link>
          </div>
          <Link to="/">
            <Button variant="ghost" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              返回首页
            </Button>
          </Link>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-16 bg-gradient-to-b from-gray-50 to-white">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold" style={{ color: ACCENT }}>认识我们</h1>
            <p className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
              以科学推动社会进步，创新与传统并重。我们致力于培养具有全球视野的科技人才，推动科学研究与技术创新。
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <main className="py-16">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          {/* Mission & Vision */}
          <section className="mb-16">
            <div className="grid lg:grid-cols-2 gap-8">
              <Card className="border" style={{ borderColor: ACCENT + "20" }}>
                <CardHeader>
                  <CardTitle style={{ color: ACCENT }}>我们的使命</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700">
                    通过卓越的教育、前沿的研究和积极的社会参与，培养能够应对全球挑战的科技领袖，
                    推动科学技术的发展，为人类社会的可持续发展做出贡献。
                  </p>
                </CardContent>
              </Card>

              <Card className="border" style={{ borderColor: ACCENT + "20" }}>
                <CardHeader>
                  <CardTitle style={{ color: ACCENT }}>我们的愿景</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700">
                    成为世界一流的科技大学，在科学研究、技术创新和人才培养方面享有国际声誉，
                    成为连接学术界与产业界的重要桥梁。
                  </p>
                </CardContent>
              </Card>
            </div>
          </section>

          {/* Key Statistics */}
          <section className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-8" style={{ color: ACCENT }}>关键数据</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {[
                { icon: <Building2 className="h-8 w-8" />, number: "4", label: "校区" },
                { icon: <Users className="h-8 w-8" />, number: "20,000+", label: "在校学生" },
                { icon: <Globe className="h-8 w-8" />, number: "50+", label: "国际合作伙伴" },
                { icon: <Award className="h-8 w-8" />, number: "100+", label: "研究项目" },
              ].map((stat, index) => (
                <Card key={index} className="text-center p-6 border" style={{ borderColor: ACCENT + "20" }}>
                  <div className="flex justify-center mb-4">
                    <div className="p-3 rounded-full" style={{ backgroundColor: ACCENT + "10", color: ACCENT }}>
                      {stat.icon}
                    </div>
                  </div>
                  <div className="text-3xl font-bold mb-2" style={{ color: ACCENT }}>{stat.number}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </Card>
              ))}
            </div>
          </section>

          {/* History */}
          <section className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-8" style={{ color: ACCENT }}>发展历程</h2>
            <Card className="border" style={{ borderColor: ACCENT + "20" }}>
              <CardContent className="p-8">
                <div className="space-y-6">
                  {[
                    { year: "1881", event: "学校创立，以培养科技人才为使命" },
                    { year: "1949", event: "重组为现代综合性理工大学" },
                    { year: "1990", event: "建立国际交流合作项目" },
                    { year: "2010", event: "启动世界一流大学建设计划" },
                    { year: "2020", event: "数字化转型，建设智慧校园" },
                  ].map((milestone, index) => (
                    <div key={index} className="flex gap-4">
                      <div className="flex-shrink-0">
                        <div className="w-16 h-16 rounded-full flex items-center justify-center text-white font-bold" style={{ backgroundColor: ACCENT }}>
                          {milestone.year.slice(-2)}
                        </div>
                      </div>
                      <div className="flex-1 pt-2">
                        <h3 className="font-semibold text-lg" style={{ color: ACCENT }}>{milestone.year}</h3>
                        <p className="text-gray-700">{milestone.event}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </section>

          {/* Leadership */}
          <section>
            <h2 className="text-3xl font-bold text-center mb-8" style={{ color: ACCENT }}>领导团队</h2>
            <div className="grid md:grid-cols-3 gap-6">
              {[
                { name: "校长", title: "张教授", description: "国际知名材料科学专家，致力于推动大学国际化发展" },
                { name: "副校长", title: "李教授", description: "计算机科学领域专家，负责学术事务和研究发展" },
                { name: "副校长", title: "王教授", description: "教育管理专家，负责学生事务和校园建设" },
              ].map((leader, index) => (
                <Card key={index} className="border" style={{ borderColor: ACCENT + "20" }}>
                  <CardContent className="p-6 text-center">
                    <div className="w-20 h-20 rounded-full mx-auto mb-4 bg-gradient-to-br from-gray-200 to-gray-300"></div>
                    <h3 className="font-semibold text-lg" style={{ color: ACCENT }}>{leader.title}</h3>
                    <p className="text-gray-600 font-medium">{leader.name}</p>
                    <p className="text-sm text-gray-600 mt-2">{leader.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>
        </div>
      </main>
    </div>
  );
}
