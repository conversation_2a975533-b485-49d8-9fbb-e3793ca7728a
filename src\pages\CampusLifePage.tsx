import React from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Home, Users, Heart, Utensils, Dumbbell, Music } from 'lucide-react';
import { Link } from 'react-router-dom';

const ACCENT = "#002fa7";

export default function CampusLifePage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 h-16 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-9 w-9 rounded-xl grid place-items-center text-white" style={{ backgroundColor: ACCENT }}>U</div>
            <Link to="/" className="font-semibold tracking-wide">理工大学 · University of Science</Link>
          </div>
          <Link to="/">
            <Button variant="ghost" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              返回首页
            </Button>
          </Link>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-16 bg-gradient-to-b from-gray-50 to-white">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold" style={{ color: ACCENT }}>校园生活</h1>
            <p className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
              宿舍与住房、学生社团、国际学生支持与心理健康服务等，打造有温度的学习共同体
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <main className="py-16">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          {/* Living */}
          <section className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-8" style={{ color: ACCENT }}>住宿生活</h2>
            <div className="grid md:grid-cols-3 gap-6">
              <Card className="border" style={{ borderColor: ACCENT + "20" }}>
                <CardHeader>
                  <Home className="h-8 w-8 mb-2" style={{ color: ACCENT }} />
                  <CardTitle style={{ color: ACCENT }}>学生宿舍</CardTitle>
                  <CardDescription>现代化宿舍设施，营造温馨的居住环境</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li>• 单人间、双人间、四人间可选</li>
                    <li>• 24小时热水供应</li>
                    <li>• 高速网络覆盖</li>
                    <li>• 洗衣房、自习室</li>
                    <li>• 24小时安保服务</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="border" style={{ borderColor: ACCENT + "20" }}>
                <CardHeader>
                  <Utensils className="h-8 w-8 mb-2" style={{ color: ACCENT }} />
                  <CardTitle style={{ color: ACCENT }}>餐饮服务</CardTitle>
                  <CardDescription>多样化餐饮选择，满足不同口味需求</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li>• 5个学生食堂</li>
                    <li>• 国际美食广场</li>
                    <li>• 清真餐厅</li>
                    <li>• 咖啡厅、茶室</li>
                    <li>• 营养健康搭配</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="border" style={{ borderColor: ACCENT + "20" }}>
                <CardHeader>
                  <Heart className="h-8 w-8 mb-2" style={{ color: ACCENT }} />
                  <CardTitle style={{ color: ACCENT }}>健康服务</CardTitle>
                  <CardDescription>全方位健康保障，关注身心健康</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li>• 校医院医疗服务</li>
                    <li>• 心理咨询中心</li>
                    <li>• 健康体检</li>
                    <li>• 急救培训</li>
                    <li>• 健康教育讲座</li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </section>

          {/* Student Organizations */}
          <section className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-8" style={{ color: ACCENT }}>学生社团</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                { icon: <Users className="h-8 w-8" />, title: "学术科技类", count: "25+", description: "科技创新、学术研究、专业竞赛" },
                { icon: <Music className="h-8 w-8" />, title: "文艺体育类", count: "30+", description: "音乐、舞蹈、戏剧、体育运动" },
                { icon: <Heart className="h-8 w-8" />, title: "公益服务类", count: "20+", description: "志愿服务、社会实践、环保行动" },
                { icon: <Users className="h-8 w-8" />, title: "兴趣爱好类", count: "35+", description: "摄影、动漫、游戏、手工制作" },
              ].map((category, index) => (
                <Card key={index} className="text-center p-6 border hover:shadow-lg transition-shadow" style={{ borderColor: ACCENT + "20" }}>
                  <div className="flex justify-center mb-4">
                    <div className="p-3 rounded-full" style={{ backgroundColor: ACCENT + "10", color: ACCENT }}>
                      {category.icon}
                    </div>
                  </div>
                  <h3 className="font-semibold text-lg mb-2" style={{ color: ACCENT }}>{category.title}</h3>
                  <div className="text-2xl font-bold mb-2" style={{ color: ACCENT }}>{category.count}</div>
                  <p className="text-gray-600 text-sm">{category.description}</p>
                </Card>
              ))}
            </div>
          </section>

          {/* Sports & Recreation */}
          <section className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-8" style={{ color: ACCENT }}>体育运动</h2>
            <Card className="border" style={{ borderColor: ACCENT + "20" }}>
              <CardContent className="p-8">
                <div className="grid lg:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-2xl font-semibold mb-4" style={{ color: ACCENT }}>体育设施</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <h4 className="font-medium">室内场馆</h4>
                        <ul className="text-sm text-gray-600 space-y-1">
                          <li>• 综合体育馆</li>
                          <li>• 游泳馆</li>
                          <li>• 健身房</li>
                          <li>• 乒乓球馆</li>
                          <li>• 羽毛球馆</li>
                        </ul>
                      </div>
                      <div className="space-y-2">
                        <h4 className="font-medium">户外场地</h4>
                        <ul className="text-sm text-gray-600 space-y-1">
                          <li>• 标准田径场</li>
                          <li>• 足球场</li>
                          <li>• 篮球场</li>
                          <li>• 网球场</li>
                          <li>• 排球场</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-2xl font-semibold mb-4" style={{ color: ACCENT }}>体育项目</h3>
                    <div className="space-y-4">
                      <Card className="p-4 border" style={{ borderColor: ACCENT + "20" }}>
                        <h4 className="font-semibold" style={{ color: ACCENT }}>校队训练</h4>
                        <p className="text-sm text-gray-600">篮球、足球、游泳等专业队伍训练</p>
                      </Card>
                      <Card className="p-4 border" style={{ borderColor: ACCENT + "20" }}>
                        <h4 className="font-semibold" style={{ color: ACCENT }}>体育课程</h4>
                        <p className="text-sm text-gray-600">必修体育课程和选修运动项目</p>
                      </Card>
                      <Card className="p-4 border" style={{ borderColor: ACCENT + "20" }}>
                        <h4 className="font-semibold" style={{ color: ACCENT }}>体育竞赛</h4>
                        <p className="text-sm text-gray-600">校内外体育比赛和运动会</p>
                      </Card>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </section>

          {/* International Students */}
          <section className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-8" style={{ color: ACCENT }}>国际学生支持</h2>
            <div className="grid md:grid-cols-3 gap-6">
              <Card className="border" style={{ borderColor: ACCENT + "20" }}>
                <CardHeader>
                  <CardTitle style={{ color: ACCENT }}>入学支持</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li>• 签证申请指导</li>
                    <li>• 机场接机服务</li>
                    <li>• 入学注册协助</li>
                    <li>• 银行开户指导</li>
                    <li>• 保险办理协助</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="border" style={{ borderColor: ACCENT + "20" }}>
                <CardHeader>
                  <CardTitle style={{ color: ACCENT }}>学习支持</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li>• 中文语言课程</li>
                    <li>• 学术写作指导</li>
                    <li>• 学习伙伴计划</li>
                    <li>• 课业辅导服务</li>
                    <li>• 文化适应指导</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="border" style={{ borderColor: ACCENT + "20" }}>
                <CardHeader>
                  <CardTitle style={{ color: ACCENT }}>生活支持</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li>• 住宿安排</li>
                    <li>• 文化交流活动</li>
                    <li>• 节日庆祝活动</li>
                    <li>• 旅游组织</li>
                    <li>• 就业指导</li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </section>

          {/* Campus Events */}
          <section>
            <h2 className="text-3xl font-bold text-center mb-8" style={{ color: ACCENT }}>校园活动</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <Card className="border" style={{ borderColor: ACCENT + "20" }}>
                <CardHeader>
                  <CardTitle style={{ color: ACCENT }}>学术活动</CardTitle>
                  <CardDescription>丰富的学术交流和科研活动</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li>• 学术讲座和研讨会</li>
                    <li>• 科技创新竞赛</li>
                    <li>• 学术论文发表会</li>
                    <li>• 国际学术会议</li>
                    <li>• 产学研交流活动</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="border" style={{ borderColor: ACCENT + "20" }}>
                <CardHeader>
                  <CardTitle style={{ color: ACCENT }}>文化活动</CardTitle>
                  <CardDescription>多彩的校园文化和艺术活动</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li>• 校园文化节</li>
                    <li>• 音乐会和戏剧演出</li>
                    <li>• 艺术展览</li>
                    <li>• 传统节日庆祝</li>
                    <li>• 国际文化交流</li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </section>
        </div>
      </main>
    </div>
  );
}
