import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, BookOpen, Users, Globe, Award, ChevronRight } from 'lucide-react';
import { Link } from 'react-router-dom';

const ACCENT = "#002fa7";

export default function AcademicsPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 h-16 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-9 w-9 rounded-xl grid place-items-center text-white" style={{ backgroundColor: ACCENT }}>U</div>
            <Link to="/" className="font-semibold tracking-wide">理工大学 · University of Science</Link>
          </div>
          <Link to="/">
            <Button variant="ghost" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              返回首页
            </Button>
          </Link>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-16 bg-gradient-to-b from-gray-50 to-white">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold" style={{ color: ACCENT }}>学术体系</h1>
            <p className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
              覆盖理学、工学、信息科学、生命科学等领域，强调跨学科与实践导向的教育体系
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <main className="py-16">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          {/* Schools & Departments */}
          <section className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-8" style={{ color: ACCENT }}>学院设置</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[
                {
                  name: "理学院",
                  description: "数学、物理、化学、生物等基础科学学科",
                  programs: ["数学", "物理学", "化学", "生物学", "地球科学"],
                  students: "3,200+"
                },
                {
                  name: "工学院",
                  description: "机械、电气、土木、材料等工程学科",
                  programs: ["机械工程", "电气工程", "土木工程", "材料科学", "航空航天"],
                  students: "4,500+"
                },
                {
                  name: "信息学院",
                  description: "计算机科学、软件工程、人工智能等",
                  programs: ["计算机科学", "软件工程", "人工智能", "数据科学", "网络安全"],
                  students: "3,800+"
                },
                {
                  name: "生命科学学院",
                  description: "生物技术、生物医学工程、药学等",
                  programs: ["生物技术", "生物医学工程", "药学", "生物信息学"],
                  students: "2,100+"
                },
                {
                  name: "环境学院",
                  description: "环境科学、环境工程、可持续发展等",
                  programs: ["环境科学", "环境工程", "可持续发展", "生态学"],
                  students: "1,800+"
                },
                {
                  name: "管理学院",
                  description: "工程管理、技术管理、创新创业等",
                  programs: ["工程管理", "技术管理", "创新创业", "项目管理"],
                  students: "2,400+"
                }
              ].map((school, index) => (
                <Card key={index} className="border hover:shadow-lg transition-shadow" style={{ borderColor: ACCENT + "20" }}>
                  <CardHeader>
                    <CardTitle style={{ color: ACCENT }}>{school.name}</CardTitle>
                    <CardDescription>{school.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4" style={{ color: ACCENT }} />
                        <span className="text-sm font-medium">{school.students} 在校生</span>
                      </div>
                      <div>
                        <p className="text-sm font-medium mb-2">主要专业：</p>
                        <div className="flex flex-wrap gap-1">
                          {school.programs.map((program, idx) => (
                            <Badge key={idx} variant="secondary" className="text-xs">
                              {program}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <Button variant="link" className="px-0 h-auto" style={{ color: ACCENT }}>
                        了解更多 <ChevronRight className="ml-1 h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>

          {/* Academic Programs */}
          <section className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-8" style={{ color: ACCENT }}>学位项目</h2>
            <div className="grid lg:grid-cols-3 gap-6">
              <Card className="border" style={{ borderColor: ACCENT + "20" }}>
                <CardHeader>
                  <CardTitle style={{ color: ACCENT }}>本科教育</CardTitle>
                  <CardDescription>4年制学士学位项目</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li>• 33个本科专业</li>
                    <li>• 跨学科课程设置</li>
                    <li>• 实习实践项目</li>
                    <li>• 国际交换机会</li>
                    <li>• 创新创业支持</li>
                  </ul>
                  <Button className="mt-4 w-full" style={{ backgroundColor: ACCENT }}>
                    查看专业目录
                  </Button>
                </CardContent>
              </Card>

              <Card className="border" style={{ borderColor: ACCENT + "20" }}>
                <CardHeader>
                  <CardTitle style={{ color: ACCENT }}>研究生教育</CardTitle>
                  <CardDescription>硕士和博士学位项目</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li>• 硕士学位项目（2-3年）</li>
                    <li>• 博士学位项目（3-5年）</li>
                    <li>• 研究导师制</li>
                    <li>• 国际合作研究</li>
                    <li>• 产学研结合</li>
                  </ul>
                  <Button className="mt-4 w-full" style={{ backgroundColor: ACCENT }}>
                    研究生招生
                  </Button>
                </CardContent>
              </Card>

              <Card className="border" style={{ borderColor: ACCENT + "20" }}>
                <CardHeader>
                  <CardTitle style={{ color: ACCENT }}>继续教育</CardTitle>
                  <CardDescription>终身学习和专业发展</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li>• 在职研究生项目</li>
                    <li>• 专业技能培训</li>
                    <li>• 企业定制课程</li>
                    <li>• 在线学习平台</li>
                    <li>• 职业发展支持</li>
                  </ul>
                  <Button className="mt-4 w-full" style={{ backgroundColor: ACCENT }}>
                    了解详情
                  </Button>
                </CardContent>
              </Card>
            </div>
          </section>

          {/* International Programs */}
          <section className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-8" style={{ color: ACCENT }}>国际化教育</h2>
            <Card className="border" style={{ borderColor: ACCENT + "20" }}>
              <CardContent className="p-8">
                <div className="grid lg:grid-cols-2 gap-8 items-center">
                  <div>
                    <h3 className="text-2xl font-semibold mb-4" style={{ color: ACCENT }}>全球合作网络</h3>
                    <p className="text-gray-700 mb-6">
                      与世界50多所知名大学建立合作关系，提供丰富的国际交流机会，培养具有全球视野的人才。
                    </p>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center">
                        <div className="text-3xl font-bold" style={{ color: ACCENT }}>50+</div>
                        <div className="text-sm text-gray-600">合作院校</div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold" style={{ color: ACCENT }}>30+</div>
                        <div className="text-sm text-gray-600">国家地区</div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold" style={{ color: ACCENT }}>1000+</div>
                        <div className="text-sm text-gray-600">交换学生</div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold" style={{ color: ACCENT }}>20+</div>
                        <div className="text-sm text-gray-600">双学位项目</div>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <Card className="p-4 border" style={{ borderColor: ACCENT + "20" }}>
                      <h4 className="font-semibold" style={{ color: ACCENT }}>交换项目</h4>
                      <p className="text-sm text-gray-600">一学期或一学年的海外学习体验</p>
                    </Card>
                    <Card className="p-4 border" style={{ borderColor: ACCENT + "20" }}>
                      <h4 className="font-semibold" style={{ color: ACCENT }}>双学位项目</h4>
                      <p className="text-sm text-gray-600">获得两所大学的学位证书</p>
                    </Card>
                    <Card className="p-4 border" style={{ borderColor: ACCENT + "20" }}>
                      <h4 className="font-semibold" style={{ color: ACCENT }}>暑期项目</h4>
                      <p className="text-sm text-gray-600">短期海外学习和文化体验</p>
                    </Card>
                    <Card className="p-4 border" style={{ borderColor: ACCENT + "20" }}>
                      <h4 className="font-semibold" style={{ color: ACCENT }}>联合研究</h4>
                      <p className="text-sm text-gray-600">与国际伙伴的科研合作项目</p>
                    </Card>
                  </div>
                </div>
              </CardContent>
            </Card>
          </section>

          {/* Academic Resources */}
          <section>
            <h2 className="text-3xl font-bold text-center mb-8" style={{ color: ACCENT }}>学术资源</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                { icon: <BookOpen className="h-8 w-8" />, title: "图书馆", description: "300万册藏书，24小时开放" },
                { icon: <Users className="h-8 w-8" />, title: "实验室", description: "100+专业实验室，先进设备" },
                { icon: <Globe className="h-8 w-8" />, title: "在线平台", description: "数字化学习资源和工具" },
                { icon: <Award className="h-8 w-8" />, title: "学术支持", description: "写作中心、辅导服务" },
              ].map((resource, index) => (
                <Card key={index} className="text-center p-6 border hover:shadow-lg transition-shadow" style={{ borderColor: ACCENT + "20" }}>
                  <div className="flex justify-center mb-4">
                    <div className="p-3 rounded-full" style={{ backgroundColor: ACCENT + "10", color: ACCENT }}>
                      {resource.icon}
                    </div>
                  </div>
                  <h3 className="font-semibold text-lg mb-2" style={{ color: ACCENT }}>{resource.title}</h3>
                  <p className="text-gray-600 text-sm">{resource.description}</p>
                </Card>
              ))}
            </div>
          </section>
        </div>
      </main>
    </div>
  );
}
