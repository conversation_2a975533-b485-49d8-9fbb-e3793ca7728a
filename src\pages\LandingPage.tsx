import React, { useEffect, useMemo, useRef, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Link } from "react-router-dom";
import {
  Search,
  Globe,
  ChevronRight,
  ChevronLeft,
  Users,
  Building2,
  BookOpen,
  FlaskConical,
  GraduationCap,
  ArrowRight,
  Instagram,
  Youtube,
  MapPin,
  Mail,
  Phone,
  ArrowUp,
  BellRing,
  Sparkles,
} from "lucide-react";

// === shadcn/ui ===
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardHeader, CardContent, CardFooter, CardTitle, CardDescription } from "@/components/ui/card";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Sheet, SheetTrigger, SheetContent } from "@/components/ui/sheet";
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
import { NavigationMenu, NavigationMenuList, NavigationMenuItem, NavigationMenuTrigger, NavigationMenuContent, NavigationMenuLink } from "@/components/ui/navigation-menu";
import { HoverCard, HoverCardTrigger, HoverCardContent } from "@/components/ui/hover-card";
import { TooltipProvider, Tooltip, TooltipTrigger, TooltipContent } from "@/components/ui/tooltip";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/components/ui/use-toast";
import { ToastAction } from "@/components/ui/toast";
import { CommandDialog, CommandInput, CommandList, CommandItem, CommandEmpty, CommandGroup } from "@/components/ui/command";
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";
import { Skeleton } from "@/components/ui/skeleton";

// 主题色：克莱因蓝（全站强调色）
const ACCENT = "#002fa7";

function cx(...classes: (string | boolean | undefined)[]): string {
  return classes.filter(Boolean).join(" ");
}

const slides = [
  { title: "发现 · 科学与未来", desc: "以严谨治学与跨学科研究为核心，连接产业与社会。", cta: "了解我们的学术体系" },
  { title: "加入我们 · 与最聪明的头脑同行", desc: "从本科到研究生，系统支持你的学习与研究之旅。", cta: "查看招生与资助" },
  { title: "研究 · 让灵感落地", desc: "聚焦关键技术突破，推动社会与产业可持续发展。", cta: "浏览研究中心" },
];

const rawMetrics = [
  { icon: <Building2 className="h-6 w-6" />, label: "校区", value: 4 },
  { icon: <Users className="h-6 w-6" />, label: "学院", value: 7 },
  { icon: <BookOpen className="h-6 w-6" />, label: "本科专业", value: 33 },
  { icon: <GraduationCap className="h-6 w-6" />, label: "研究生院", value: 7 },
  { icon: <FlaskConical className="h-6 w-6" />, label: "研究中心", value: 30 },
  { icon: <Users className="h-6 w-6" />, label: "在校生", value: 20000, suffix: "+" },
];

const news = [
  { tag: "研究", title: "AI 驱动的材料发现加速平台发布", date: "2025-07-22", summary: "团队提出面向实验室的端到端自动化验证流程。" },
  { tag: "校园", title: "秋季开放日报名开启", date: "2025-08-10", summary: "面向高中生与家长开放实验室、讲座与校园参观路线。" },
  { tag: "合作", title: "与产业伙伴共建半导体联合实验室", date: "2025-06-18", summary: "围绕先进封装与功率器件展开联合攻关。" },
];

const events = [
  { date: "2025-09-05", title: "开学典礼与新生指导", detail: "主会场与各学院分会场同步进行，提供校园服务集市。" },
  { date: "2025-09-20", title: "研究前沿系列讲座：量子信息", detail: "来自产业与学界的演讲嘉宾共同探讨应用落地。" },
  { date: "2025-10-02", title: "国际学生欢迎周", detail: "签证、住宿、课程注册与文化活动的一站式支持。" },
];

export default function LandingPage() {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);
  const [activeSlide, setActiveSlide] = useState(0);
  const [tab, setTab] = useState("news");
  const [progress, setProgress] = useState(0);
  const [loadingMore, setLoadingMore] = useState(false);
  const { toast } = useToast();

  // 轮播自动播放
  useEffect(() => {
    const id = setInterval(() => setActiveSlide((i) => (i + 1) % slides.length), 6000);
    return () => clearInterval(id);
  }, []);

  // 滚动进度条
  useEffect(() => {
    const onScroll = () => {
      const h = document.documentElement;
      const scrolled = h.scrollTop;
      const max = h.scrollHeight - h.clientHeight;
      setProgress(max ? (scrolled / max) * 100 : 0);
    };
    onScroll();
    window.addEventListener("scroll", onScroll);
    return () => window.removeEventListener("scroll", onScroll);
  }, []);

  // Command + K 打开搜索
  useEffect(() => {
    const onKey = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key.toLowerCase() === "k") {
        e.preventDefault();
        setSearchOpen(true);
      }
    };
    window.addEventListener("keydown", onKey);
    return () => window.removeEventListener("keydown", onKey);
  }, []);

  const slide = useMemo(() => slides[activeSlide], [activeSlide]);

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-white text-gray-900">
        {/* 顶部滚动进度条 */}
        <div className="fixed top-0 left-0 right-0 z-[60]">
          <Progress value={progress} className="h-1 rounded-none" style={{ ["--primary" as any]: ACCENT }} />
        </div>

        {/* 公告条 */}
        <div className="w-full bg-white border-b" style={{ borderColor: ACCENT + "20" }}>
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 h-9 flex items-center gap-2 text-sm">
            <BellRing className="h-4 w-4" style={{ color: ACCENT }} />
            <span className="hidden sm:inline">2026 学年本科申请早鸟批正在开放。</span>
            <Button variant="link" className="px-2" style={{ color: ACCENT }} onClick={() => window.scrollTo({ top: document.getElementById("admissions")?.offsetTop || 0, behavior: "smooth" })}>
              立即查看
            </Button>
            <div className="ml-auto hidden md:flex items-center gap-2 text-xs text-gray-500">按 <kbd className="px-1 py-0.5 border rounded">⌘K</kbd> / <kbd className="px-1 py-0.5 border rounded">Ctrl K</kbd> 打开全站搜索</div>
          </div>
        </div>

        {/* 跳到内容 */}
        <a href="#main" className="sr-only focus:not-sr-only focus:fixed focus:top-3 focus:left-3 focus:z-50 bg-white border rounded-md px-3 py-1">跳到主要内容</a>

        {/* 顶部受众快速入口 */}
        <div className="w-full border-b bg-white/70 backdrop-blur supports-[backdrop-filter]:bg-white/60" style={{ borderColor: ACCENT + "20" }}>
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 flex items-center justify-between h-10 text-sm">
            <div className="flex gap-3 overflow-x-auto no-scrollbar">
              {["未来学生", "在校生", "校友", "教职员工", "企业 · 研究者", "社区", "媒体"].map((item) => (
                <Button key={item} variant="ghost" className="h-7 px-2 text-gray-700">{item}</Button>
              ))}
            </div>
            <div className="flex items-center gap-2">
              <Button variant="ghost" className="h-7 px-2 text-gray-700"><MapPin className="h-4 w-4" /> 校区与交通</Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-7 px-2 text-gray-700"><Globe className="h-4 w-4" /> 中文 / EN</Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>中文</DropdownMenuItem>
                  <DropdownMenuItem>English</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>

        {/* 主导航 */}
        <header className="sticky top-0 z-40 bg-white/80 backdrop-blur border-b" style={{ borderColor: ACCENT + "20" }}>
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 h-16 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="h-9 w-9 rounded-xl grid place-items-center text-white" style={{ backgroundColor: ACCENT }}>U</div>
              <Link to="/" className="font-semibold tracking-wide">理工大学 · University of Science</Link>
            </div>

            <NavigationMenu className="hidden md:block">
              <NavigationMenuList>
                {[
                  { label: "认识我们", path: "/about" },
                  { label: "学术", path: "/academics" },
                  { label: "研究", path: "/research" },
                  { label: "校园生活", path: "/campus-life" },
                  { label: "招生与资助", path: "/admissions" }
                ].map((item) => (
                  <NavigationMenuItem key={item.label}>
                    <Link to={item.path}>
                      <NavigationMenuTrigger>{item.label}</NavigationMenuTrigger>
                    </Link>
                  </NavigationMenuItem>
                ))}
              </NavigationMenuList>
            </NavigationMenu>

            <div className="flex items-center gap-2">
              <Dialog open={searchOpen} onOpenChange={setSearchOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline" className="h-9"><Search className="h-5 w-5" /></Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[640px]">
                  <DialogHeader>
                    <DialogTitle>站内搜索</DialogTitle>
                    <DialogDescription>输入关键词，或按 Esc 关闭。</DialogDescription>
                  </DialogHeader>
                  <CommandDialog open={true} onOpenChange={setSearchOpen}>
                    <CommandInput placeholder="搜索学院、研究、新闻…" />
                    <CommandList>
                      <CommandEmpty>没有找到结果</CommandEmpty>
                      <CommandGroup heading="快捷入口">
                        {[
                          "学院一览",
                          "奖学金",
                          "研究平台",
                          "校园地图",
                        ].map((x) => (
                          <CommandItem key={x}>{x}</CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </CommandDialog>
                </DialogContent>
              </Dialog>

              <HoverCard openDelay={150} closeDelay={100}>
                <HoverCardTrigger asChild>
                  <Button className="hidden md:inline-flex" style={{ backgroundColor: ACCENT }}>
                    立即申请 <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </HoverCardTrigger>
                <HoverCardContent className="w-80">
                  <div className="flex gap-3">
                    <Sparkles className="h-5 w-5" style={{ color: ACCENT }} />
                    <div>
                      <div className="font-medium">现在申请享优先审核</div>
                      <p className="text-sm text-gray-600">建议准备成绩单与两封推荐信；下一截止日：<strong>2025-10-01</strong></p>
                    </div>
                  </div>
                </HoverCardContent>
              </HoverCard>

              {/* 移动端抽屉菜单 */}
              <Sheet open={mobileOpen} onOpenChange={setMobileOpen}>
                <SheetTrigger asChild>
                  <Button className="md:hidden" variant="outline" aria-label="打开菜单">
                    <svg className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16"/></svg>
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="w-[320px]">
                  <div className="mt-6 grid gap-2">
                    {[
                      { label: "认识我们", path: "/about" },
                      { label: "学术", path: "/academics" },
                      { label: "研究", path: "/research" },
                      { label: "校园生活", path: "/campus-life" },
                      { label: "招生与资助", path: "/admissions" }
                    ].map((item) => (
                      <Link key={item.label} to={item.path}>
                        <Button variant="ghost" className="justify-start w-full">{item.label}</Button>
                      </Link>
                    ))}
                    <Separator className="my-2" />
                    <Link to="/admissions">
                      <Button className="w-full" style={{ backgroundColor: ACCENT }}>立即申请</Button>
                    </Link>
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </header>

        {/* 大图横幅（TUS 素材） */}
        <section aria-label="校园大图" className="relative">
          <AspectRatio ratio={16/9}>
            <img src="https://www.tus.ac.jp/en/assets/images/Discover-TUS-slider-image-1.jpg" alt="东京理科大学校园建筑（示意）" className="h-full w-full object-cover" />
          </AspectRatio>
          {/* 渐隐/暗角与叠字 */}
          <div className="absolute inset-0 pointer-events-none" style={{ background: "linear-gradient(to bottom, rgba(0,0,0,0.55), rgba(0,0,0,0.0) 35%, rgba(0,0,0,0.0) 65%, rgba(0,0,0,0.6))" }} />
          <div className="absolute inset-0 pointer-events-none" style={{ background: "radial-gradient(ellipse at center, rgba(0,0,0,0) 45%, rgba(0,0,0,0.35) 100%)" }} />
          <div className="absolute inset-0">
            <div className="mx-auto max-w-7xl h-full px-4 sm:px-6 lg:px-8 flex items-end lg:items-center">
              <motion.div initial={{ y: 24, opacity: 0 }} whileInView={{ y: 0, opacity: 1 }} viewport={{ once: true }} transition={{ duration: 0.6 }} className="pb-8 lg:pb-0">
                <p className="text-white/80 text-[11px] tracking-[0.3em] uppercase">Tokyo University of Science</p>
                <h1 className="mt-2 text-3xl sm:text-4xl lg:text-5xl font-bold text-white" style={{ textShadow: "0 6px 24px rgba(0,0,0,0.45), 0 2px 8px rgba(0,0,0,0.35)" }}>Discover TUS</h1>
                <p className="mt-2 text-white/90 max-w-2xl" style={{ textShadow: "0 2px 8px rgba(0,0,0,0.4)" }}>以科学推动社会进步 · 创新与传统并重。</p>
              </motion.div>
            </div>
          </div>
        </section>

        <main id="main">
          {/* HERO 卡片轮播（shadcn Carousel） */}
          <section className="relative">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-10 lg:py-16">
              <Card className="overflow-hidden border" style={{ borderColor: ACCENT + "20" }}>
                <div className="grid lg:grid-cols-2">
                  <div className="p-8 sm:p-12 lg:p-16 flex flex-col gap-6">
                    <AnimatePresence mode="wait">
                      <motion.h2 key={slide.title} initial={{ y: 16, opacity: 0 }} animate={{ y: 0, opacity: 1 }} exit={{ y: -16, opacity: 0 }} transition={{ duration: 0.5 }} className="text-3xl sm:text-4xl lg:text-5xl font-bold leading-tight" style={{ color: ACCENT }}>
                        {slide.title}
                      </motion.h2>
                    </AnimatePresence>
                    <AnimatePresence mode="wait">
                      <motion.p key={slide.desc} initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }} transition={{ duration: 0.5, delay: 0.1 }} className="text-gray-700 text-lg">{slide.desc}</motion.p>
                    </AnimatePresence>
                    <div className="flex gap-3 pt-2">
                      <Button className="rounded-2xl" style={{ backgroundColor: ACCENT }}>{slide.cta} <ChevronRight className="ml-2 h-4 w-4" /></Button>
                      <Button variant="outline" className="rounded-2xl" style={{ borderColor: ACCENT + "40" }}>校园一分钟 <Youtube className="ml-2 h-4 w-4" /></Button>
                    </div>
                    <div className="flex items-center gap-2 pt-4">
                      {slides.map((_, i) => (
                        <button key={i} onClick={() => setActiveSlide(i)} aria-label={`切换到第 ${i + 1} 张`} className={cx("h-2 w-6 rounded-full transition-colors", i === activeSlide ? "" : "bg-gray-200")} style={{ backgroundColor: i === activeSlide ? ACCENT : undefined }} />
                      ))}
                    </div>
                  </div>
                  <div className="relative min-h-[320px] lg:min-h-[460px]">
                    <Carousel opts={{ align: "start" }} className="w-full h-full">
                      <CarouselContent>
                        {slides.map((s, idx) => (
                          <CarouselItem key={idx} className="pl-0">
                            <div className="h-full w-full grid place-items-center">
                              <div className="aspect-[4/3] w-11/12 rounded-2xl border bg-white/70 backdrop-blur flex items-center justify-center" style={{ borderColor: ACCENT + "20" }}>
                                <div className="text-center p-6">
                                  <p className="text-sm uppercase tracking-widest text-gray-500">Campus Visual</p>
                                  <h3 className="mt-2 text-2xl font-semibold" style={{ color: ACCENT }}>{s.title}</h3>
                                  <p className="mt-2 text-gray-600">这里可替换为校景图、实验室、学生活动等视觉素材。</p>
                                </div>
                              </div>
                            </div>
                          </CarouselItem>
                        ))}
                      </CarouselContent>
                      <CarouselPrevious className="left-3" />
                      <CarouselNext className="right-3" />
                    </Carousel>
                  </div>
                </div>
              </Card>
            </div>
          </section>

          {/* 关键指标（带数字滚动与提示） */}
          <Metrics />

          {/* 学术与招生 CTA 区 */}
          <section id="academics" className="py-12 lg:py-16">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 grid lg:grid-cols-2 gap-6">
              <Card className="bg-[rgba(0,47,167,0.04)] border" style={{ borderColor: ACCENT + "20" }}>
                <CardHeader>
                  <CardTitle style={{ color: ACCENT }}>学术体系</CardTitle>
                  <CardDescription>覆盖理学、工学、信息科学、生命科学等领域，强调跨学科与实践导向。</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {["学院一览", "专业设置", "课程大纲", "国际交换"].map((t) => (
                      <Button key={t} variant="outline" className="rounded-full" style={{ borderColor: ACCENT + "40" }}>
                        {t} <ChevronRight className="ml-1 h-4 w-4" />
                      </Button>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card id="admissions" className="border" style={{ borderColor: ACCENT + "20" }}>
                <CardHeader>
                  <CardTitle style={{ color: ACCENT }}>招生与资助</CardTitle>
                  <CardDescription>面向本科与研究生的多元入学途径与奖助体系，为学术发展提供坚实支持。</CardDescription>
                </CardHeader>
                <CardContent className="flex gap-3">
                  <Button className="rounded-2xl" style={{ backgroundColor: ACCENT }}>立即申请 <ArrowRight className="ml-2 h-4 w-4" /></Button>
                  <Button variant="outline" className="rounded-2xl" style={{ borderColor: ACCENT + "40" }}>奖学金与助学 <ChevronRight className="ml-1 h-4 w-4" /></Button>
                </CardContent>
              </Card>
            </div>
          </section>

          {/* 新闻 & 活动（Tabs + 加载更多 + Skeleton） */}
          <section className="py-12 lg:py-16 bg-white" aria-label="新闻与活动">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold" style={{ color: ACCENT }}>最新动态</h2>
              </div>

              <Tabs value={tab} onValueChange={setTab} className="mt-4">
                <TabsList>
                  <TabsTrigger value="news">新闻</TabsTrigger>
                  <TabsTrigger value="events">活动</TabsTrigger>
                </TabsList>
                <TabsContent value="news">
                  <div className="mt-4 grid md:grid-cols-3 gap-6">
                    {news.map((n, idx) => (
                      <Card key={idx} className="border hover:shadow-sm transition-shadow" style={{ borderColor: ACCENT + "20" }}>
                        <div className="h-36 bg-gradient-to-br from-gray-50 to-gray-100" aria-hidden />
                        <CardContent className="p-5">
                          <div className="flex items-center justify-between text-sm">
                            <Badge variant="secondary" className="font-medium" style={{ color: ACCENT, backgroundColor: ACCENT + "14", borderColor: ACCENT + "33" }}>{n.tag}</Badge>
                            <time className="text-gray-500">{n.date}</time>
                          </div>
                          <CardTitle className="mt-2 text-lg">{n.title}</CardTitle>
                          <p className="mt-1 text-gray-600 text-sm">{n.summary}</p>
                          <Button variant="link" className="px-0" style={{ color: ACCENT }}>阅读更多 <ChevronRight className="ml-1 h-4 w-4" /></Button>
                        </CardContent>
                      </Card>
                    ))}
                    {loadingMore && Array.from({ length: 3 }).map((_, i) => (
                      <Card key={`s-${i}`} className="border" style={{ borderColor: ACCENT + "20" }}>
                        <Skeleton className="h-36 w-full" />
                        <CardContent className="p-5 space-y-3">
                          <Skeleton className="h-4 w-2/3" />
                          <Skeleton className="h-4 w-full" />
                          <Skeleton className="h-4 w-5/6" />
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                  <div className="mt-6 text-center">
                    <Button
                      variant="outline"
                      style={{ borderColor: ACCENT + "40" }}
                      onClick={() => {
                        setLoadingMore(true);
                        setTimeout(() => setLoadingMore(false), 1200);
                      }}
                    >
                      加载更多
                    </Button>
                  </div>
                </TabsContent>
                <TabsContent value="events">
                  <div className="mt-4 grid md:grid-cols-3 gap-6">
                    {events.map((e, idx) => (
                      <Card key={idx} className="border hover:shadow-sm transition-shadow" style={{ borderColor: ACCENT + "20" }}>
                        <div className="h-36 bg-gradient-to-br from-gray-50 to-gray-100" aria-hidden />
                        <CardContent className="p-5">
                          <div className="flex items-center justify-between text-sm">
                            <Badge variant="outline" style={{ borderColor: ACCENT + "33", color: ACCENT }}>活动</Badge>
                            <time className="text-gray-500">{e.date}</time>
                          </div>
                          <CardTitle className="mt-2 text-lg">{e.title}</CardTitle>
                          <p className="mt-1 text-gray-600 text-sm">{e.detail}</p>
                          <Button variant="link" className="px-0" style={{ color: ACCENT }}>查看详情 <ChevronRight className="ml-1 h-4 w-4" /></Button>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </section>

          {/* 研究专区 */}
          <section id="research" className="py-12 lg:py-16">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
              <Card className="border bg-gradient-to-b from-white to-gray-50" style={{ borderColor: ACCENT + "20" }}>
                <CardContent className="p-8 lg:p-12">
                  <div className="lg:flex items-start gap-10">
                    <div className="lg:w-1/2">
                      <h2 className="text-2xl font-bold" style={{ color: ACCENT }}>面向社会需求的科研</h2>
                      <p className="mt-2 text-gray-700">以重大科学问题与产业关键技术为牵引，建设开放的交叉研究平台，促进知识转化与人才培养。</p>
                      <div className="mt-6 grid sm:grid-cols-2 gap-4">
                        {[
                          { title: "先进材料", desc: "从计算预测到实验验证的闭环研发。" },
                          { title: "信息与智能", desc: "大模型、边缘智能与可信 AI。" },
                          { title: "生命与健康", desc: "合成生物学与生物医工交叉。" },
                          { title: "能源与环境", desc: "零碳技术与循环经济。" },
                        ].map((i) => (
                          <Card key={i.title} className="border" style={{ borderColor: ACCENT + "20" }}>
                            <CardContent className="p-4">
                              <h3 className="font-semibold" style={{ color: ACCENT }}>{i.title}</h3>
                              <p className="text-gray-600 mt-1 text-sm">{i.desc}</p>
                              <Button variant="link" className="px-0" style={{ color: ACCENT }}>了解更多 <ChevronRight className="ml-1 h-4 w-4" /></Button>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>
                    <div className="lg:w-1/2 mt-8 lg:mt-0">
                      <AspectRatio ratio={4/3}>
                        <div className="w-full h-full rounded-2xl border bg-white grid place-items-center" style={{ borderColor: ACCENT + "20" }}>
                          <div className="text-center p-6">
                            <FlaskConical className="h-10 w-10 mx-auto" style={{ color: ACCENT }} />
                            <h3 className="mt-2 text-xl font-semibold" style={{ color: ACCENT }}>科研故事 / 视频位</h3>
                            <p className="mt-2 text-gray-600">可放置实验室探访、成果转化案例或研究者访谈。</p>
                            <Button className="mt-3" style={{ backgroundColor: ACCENT }}>观看精选 <Youtube className="ml-2 h-4 w-4" /></Button>
                          </div>
                        </div>
                      </AspectRatio>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </section>

          {/* 校园生活 + FAQ（Accordion） & 社媒 */}
          <section id="life" className="py-12 lg:py-16 bg-white">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
              <div className="grid lg:grid-cols-2 gap-8 items-start">
                <div>
                  <h2 className="text-2xl font-bold" style={{ color: ACCENT }}>校园生活</h2>
                  <p className="mt-2 text-gray-700">宿舍与住房、学生社团、国际学生支持与心理健康服务等，打造有温度的学习共同体。</p>
                  <Accordion type="single" collapsible className="mt-6">
                    {[{q:"宿舍与住房",a:"校内外多种房型与价格区间，提供短租名额。"},{q:"学生社团",a:"覆盖学术、艺术、体育、公益等 100+ 社团。"},{q:"国际学生支持",a:"签证、保险、语言支持与文化活动。"},{q:"体育与健康",a:"运动场馆预订、健康门诊与心理咨询。"}].map((x, i) => (
                      <AccordionItem key={i} value={`item-${i}`}>
                        <AccordionTrigger>{x.q}</AccordionTrigger>
                        <AccordionContent>{x.a}</AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </div>
                <div>
                  <div className="flex items-center justify-between">
                    <h3 className="text-xl font-semibold" style={{ color: ACCENT }}>Instagram 精选</h3>
                    <Button variant="link" className="px-0" style={{ color: ACCENT }}>关注我们 <Instagram className="ml-2 h-4 w-4" /></Button>
                  </div>
                  <div className="mt-3 grid grid-cols-3 gap-2">
                    {Array.from({ length: 6 }).map((_, i) => (
                      <div key={i} className="aspect-square rounded-xl bg-gradient-to-br from-gray-50 to-gray-100 border" style={{ borderColor: ACCENT + "15" }} />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </section>
        </main>

        {/* 页脚 */}
        <footer className="mt-12 border-t bg-white" style={{ borderColor: ACCENT + "20" }}>
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12">
            <div className="grid md:grid-cols-4 gap-8">
              <div>
                <div className="h-10 w-10 rounded-xl grid place-items-center text-white" style={{ backgroundColor: ACCENT }}>U</div>
                <p className="mt-3 text-sm text-gray-600">以科学精神服务社会，与世界共享知识与创新。</p>
                <div className="mt-3 space-y-1 text-sm text-gray-700">
                  <div className="flex items-center gap-2"><MapPin className="h-4 w-4" /> 〇〇市〇〇区 学院路 1 号</div>
                  <div className="flex items-center gap-2"><Phone className="h-4 w-4" /> +81-00-0000-0000</div>
                  <div className="flex items-center gap-2"><Mail className="h-4 w-4" /> <EMAIL></div>
                </div>
              </div>
              <div>
                <h4 className="font-semibold" style={{ color: ACCENT }}>快速入口</h4>
                <ul className="mt-3 space-y-2 text-sm">
                  {["学院与专业", "研究平台", "图书馆", "校历", "校园地图"].map((t) => (
                    <li key={t}><a href="#" className="hover:underline">{t}</a></li>
                  ))}
                </ul>
              </div>
              <div>
                <h4 className="font-semibold" style={{ color: ACCENT }}>政策与帮助</h4>
                <ul className="mt-3 space-y-2 text-sm">
                  {["网站地图", "隐私政策", "Cookie 政策", "社交媒体政策", "信息公开"].map((t) => (
                    <li key={t}><a href="#" className="hover:underline">{t}</a></li>
                  ))}
                </ul>
              </div>
              <NewsletterForm />
            </div>

            <div className="mt-10 flex flex-col sm:flex-row items-center justify-between gap-3 text-sm text-gray-600">
              <p>© {new Date().getFullYear()} University of Science. 保留所有权利。</p>
              <div className="flex items-center gap-3">
                <a href="#" className="hover:underline">无障碍声明</a>
                <a href="#" className="hover:underline">联系网站管理员</a>
              </div>
            </div>
          </div>
        </footer>

        {/* 返回顶部 */}
        <BackToTop />
      </div>
    </TooltipProvider>
  );
}

// 指标组件：带数字滚动和 Tooltip
function Metrics() {
  const [vals, setVals] = useState(rawMetrics.map(() => 0));
  const started = useRef(false);

  useEffect(() => {
    const onScroll = () => {
      if (started.current) return;
      const el = document.getElementById("metrics");
      if (!el) return;
      const rect = el.getBoundingClientRect();
      if (rect.top < window.innerHeight * 0.8) {
        started.current = true;
        animateValues();
      }
    };
    window.addEventListener("scroll", onScroll);
    onScroll();
    return () => window.removeEventListener("scroll", onScroll);
  }, []);

  const animateValues = () => {
    const duration = 900;
    const start = performance.now();
    const from = rawMetrics.map(() => 0);
    const to = rawMetrics.map((m) => m.value);
    const tick = (t: number) => {
      const pct = Math.min(1, (t - start) / duration);
      setVals(from.map((v, i) => Math.floor(v + (to[i] - v) * pct)));
      if (pct < 1) requestAnimationFrame(tick);
    };
    requestAnimationFrame(tick);
  };

  return (
    <section id="about" aria-label="关键指标" className="py-10 lg:py-14 bg-white" id="metrics">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4">
          {rawMetrics.map((m, i) => (
            <Tooltip key={m.label}>
              <TooltipTrigger asChild>
                <Card className="border p-4 text-center hover:shadow-sm transition-shadow cursor-default" style={{ borderColor: ACCENT + "20" }}>
                  <div className="h-10 w-10 rounded-xl mx-auto grid place-items-center text-white" style={{ backgroundColor: ACCENT }}>{m.icon}</div>
                  <div className="mt-3 text-2xl font-bold" style={{ color: ACCENT }}>{vals[i].toLocaleString()}{m.suffix || ""}</div>
                  <div className="text-sm text-gray-600">{m.label}</div>
                </Card>
              </TooltipTrigger>
              <TooltipContent>学校关键规模指标</TooltipContent>
            </Tooltip>
          ))}
        </div>
      </div>
    </section>
  );
}

// 订阅表单：shadcn 表单与 toast
function NewsletterForm() {
  const { toast } = useToast();
  const [email, setEmail] = useState("");
  return (
    <div>
      <h4 className="font-semibold" style={{ color: ACCENT }}>订阅更新</h4>
      <p className="mt-2 text-sm text-gray-600">订阅新闻与活动预告。</p>
      <form
        className="mt-3 flex gap-2"
        onSubmit={(e) => {
          e.preventDefault();
          toast({ title: "订阅成功", description: `我们将把更新发送到：${email}`, action: <ToastAction altText="撤销">撤销</ToastAction> });
          setEmail("");
        }}
      >
        <Input type="email" required placeholder="输入邮箱" value={email} onChange={(e) => setEmail(e.target.value)} className="flex-1" />
        <Button type="submit" style={{ backgroundColor: ACCENT }}>订阅</Button>
      </form>
    </div>
  );
}

function BackToTop() {
  const [show, setShow] = useState(false);
  useEffect(() => {
    const onScroll = () => setShow(window.scrollY > 320);
    window.addEventListener("scroll", onScroll);
    return () => window.removeEventListener("scroll", onScroll);
  }, []);
  return (
    <AnimatePresence>
      {show && (
        <motion.button
          initial={{ opacity: 0, y: 16 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 16 }}
          onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
          className="fixed bottom-5 right-5 z-40 p-3 rounded-full text-white shadow-lg"
          style={{ backgroundColor: ACCENT }}
          aria-label="返回顶部"
        >
          <ArrowUp className="h-5 w-5" />
        </motion.button>
      )}
    </AnimatePresence>
  );
}
